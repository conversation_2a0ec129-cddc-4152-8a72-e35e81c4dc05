import React from 'react';
import AntdTable from '../components/AntdTable';
import { useContentHeight } from '../hooks/useContentHeight';

interface TaskTablePageProps {
  contentHeight?: number;
}

/**
 * 任务管理页面
 * 展示完整的任务管理表格功能
 */
const TaskTablePage: React.FC<TaskTablePageProps> = ({ contentHeight }) => {
  const finalContentHeight = useContentHeight(contentHeight);

  return <AntdTable contentHeight={finalContentHeight} />;
};

export default TaskTablePage;
