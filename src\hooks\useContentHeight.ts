import { useOutletContext } from 'react-router-dom';

interface OutletContext {
  contentHeight: number;
}

/**
 * 自定义Hook：获取内容区域高度
 * 支持从props或Outlet context中获取contentHeight
 * 
 * @param propContentHeight - 通过props传递的高度
 * @returns 最终的内容高度
 */
export const useContentHeight = (propContentHeight?: number): number => {
  // 尝试从context获取contentHeight
  let contextContentHeight: number | undefined;
  
  try {
    const context = useOutletContext<OutletContext>();
    contextContentHeight = context?.contentHeight;
  } catch {
    // 如果不在Outlet context中，忽略错误
    contextContentHeight = undefined;
  }

  // 优先级：props > context > 默认值
  return propContentHeight ?? contextContentHeight ?? 600;
};
