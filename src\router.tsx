import { createBrowserRouter } from 'react-router-dom';
import { DefaultLayout } from './layouts/DefaultLayout';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/UserManagementPage';
import TeamCollaborationPage from './pages/TeamCollaborationPage';
import ProTableRefactored from './components/ProTableRefactored';
import TestRefactored from './components/TestRefactored';
import VTable from './components/VTable';
import TableWithIndeterminate from './components/TableWithIndeterminate';
import VTableConfigComparison from './components/VTableConfigComparison';
import VTableTest from './components/VTableTest';
import VTableSimple from './components/VTableSimple';
import VTableEmptyStateTest from './components/VTableEmptyStateTest';
import TaskTablePage from './pages/TaskTablePage';
import GroupManagementTest from './components/GroupManagementTest';
import TaskGroupSelectTest from './components/TaskGroupSelectTest';
import CacheTestPage from './pages/CacheTestPage';

export const router = createBrowserRouter([
  {
    path: '/',
    Component: DefaultLayout,
    children: [
      {
        index: true,
        Component: () => <DashboardPage />,
      },
      {
        path: 'dashboard',
        Component: () => <DashboardPage />,
      },
      {
        path: 'users',
        Component: () => <UserManagementPage />,
      },
      {
        path: 'tasks',
        Component: () => <TaskTablePage />,
      },
      {
        path: 'teams',
        Component: () => <TeamCollaborationPage />,
      },
      {
        path: 'cache-test',
        Component: () => <CacheTestPage />,
      },
      {
        path: 'protable',
        Component: () => <ProTableRefactored />,
      },
      {
        path: 'test',
        Component: TestRefactored,
      },
    ],
  },
  {
    path: '*',
    Component: () => <div>404 Not Found</div>,
  },
  {
    path: '/vtable',
    Component: () => <VTable />,
  },
  {
    path: '/table',
    Component: () => <TableWithIndeterminate />,
  },
  {
    path: '/vtable-config',
    Component: () => <VTableConfigComparison />,
  },
  {
    path: '/vtable-test',
    Component: () => <VTableTest />,
  },
  {
    path: '/vtable-simple',
    Component: () => <VTableSimple />,
  },
  {
    path: '/vtable-empty-test',
    Component: () => <VTableEmptyStateTest />,
  },
  {
    path: '/group-test',
    Component: () => <GroupManagementTest />,
  },
  {
    path: '/task-group-select-test',
    Component: () => <TaskGroupSelectTest />,
  },
]);
