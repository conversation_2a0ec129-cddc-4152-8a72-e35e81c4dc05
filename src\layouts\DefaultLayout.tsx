import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Badge, Breadcrumb, Tabs, Switch, Tooltip } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useDebouncedCallback } from '../hooks/useDebounce';
import styles from './DefaultLayout.module.css';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  TeamOutlined,
  BellOutlined,
  LogoutOutlined,
  ProfileOutlined,
  HomeOutlined,
  RightOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;

interface TabItem {
  key: string;
  label: string;
  closable?: boolean;
}

// 缓存的页面组件接口
interface CachedComponent {
  key: string;
  component: React.ReactNode;
  timestamp: number;
}

export const DefaultLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState('dashboard');
  const [tabs, setTabs] = useState<TabItem[]>([
    {
      key: 'dashboard',
      label: '仪表板',
      closable: false,
    },
  ]);

  // 缓存相关状态
  const [cacheEnabled, setCacheEnabled] = useState(true);
  const [cachedComponents, setCachedComponents] = useState<Map<string, CachedComponent>>(new Map());
  const [animationKey, setAnimationKey] = useState(0);

  const navigate = useNavigate();
  const location = useLocation();
  const contentRef = useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = useState<number>(0);

  // 懒加载的页面组件 - 使用useMemo避免重复创建
  const DashboardPage = React.useMemo(() => React.lazy(() => import('../pages/DashboardPage')), []);
  const UserManagementPage = React.useMemo(() => React.lazy(() => import('../pages/UserManagementPage')), []);
  const TaskTablePage = React.useMemo(() => React.lazy(() => import('../pages/TaskTablePage')), []);
  const TeamCollaborationPage = React.useMemo(() => React.lazy(() => import('../pages/TeamCollaborationPage')), []);
  const CacheTestPage = React.useMemo(() => React.lazy(() => import('../pages/CacheTestPage')), []);

  // 页面组件映射
  const getPageComponent = useCallback((key: string) => {
    const componentMap: Record<string, React.ReactNode> = {
      dashboard: (
        <React.Suspense fallback={<div>加载中...</div>}>
          <DashboardPage />
        </React.Suspense>
      ),
      users: (
        <React.Suspense fallback={<div>加载中...</div>}>
          <UserManagementPage />
        </React.Suspense>
      ),
      tasks: (
        <React.Suspense fallback={<div>加载中...</div>}>
          <TaskTablePage contentHeight={contentHeight} />
        </React.Suspense>
      ),
      teams: (
        <React.Suspense fallback={<div>加载中...</div>}>
          <TeamCollaborationPage />
        </React.Suspense>
      ),
      'cache-test': (
        <React.Suspense fallback={<div>加载中...</div>}>
          <CacheTestPage />
        </React.Suspense>
      ),
    };

    return componentMap[key] || null;
  }, [DashboardPage, UserManagementPage, TaskTablePage, contentHeight, TeamCollaborationPage, CacheTestPage]);

  // 获取或创建缓存的组件
  const getCachedComponent = useCallback((key: string) => {
    if (!cacheEnabled) {
      return getPageComponent(key);
    }

    const cached = cachedComponents.get(key);
    if (cached) {
      return cached.component;
    }

    const component = getPageComponent(key);
    const newCached: CachedComponent = {
      key,
      component,
      timestamp: Date.now(),
    };

    setCachedComponents(prev => new Map(prev).set(key, newCached));
    return component;
  }, [cacheEnabled, cachedComponents, getPageComponent]);

  // 清除指定标签页的缓存
  const clearTabCache = useCallback((key: string) => {
    setCachedComponents(prev => {
      const newMap = new Map(prev);
      newMap.delete(key);
      return newMap;
    });
  }, []);

  // 清除所有缓存
  const clearAllCache = useCallback(() => {
    setCachedComponents(new Map());
  }, []);

  // 初始化标签页状态
  useEffect(() => {
    const path = location.pathname;
    const tabLabels = {
      '/': 'dashboard',
      '/dashboard': 'dashboard',
      '/users': 'users',
      '/tasks': 'tasks',
      '/teams': 'teams',
      '/cache-test': 'cache-test',
    };

    const pageTitles = {
      '/': '仪表板',
      '/dashboard': '仪表板',
      '/users': '用户管理',
      '/tasks': '任务管理',
      '/teams': '团队协作',
      '/cache-test': '缓存测试',
    };

    const currentKey = tabLabels[path as keyof typeof tabLabels] || 'dashboard';
    const currentLabel = pageTitles[path as keyof typeof pageTitles] || '仪表板';

    setActiveTabKey(currentKey);

    // 确保当前页面的标签页存在
    setTabs(prev => {
      const existingTab = prev.find(tab => tab.key === currentKey);
      if (!existingTab && currentKey !== 'dashboard') {
        return [
          ...prev,
          {
            key: currentKey,
            label: currentLabel,
            closable: true,
          },
        ];
      }
      return prev;
    });
  }, [location.pathname]);

  // 创建高度更新函数
  const updateContentHeight = useCallback(() => {
    if (contentRef.current) {
      const height = contentRef.current.offsetHeight;
      // const clientHeight = contentRef.current.clientHeight;
      // const scrollHeight = contentRef.current.scrollHeight;
      // console.log('Content高度信息:', {
      //   offsetHeight: height,
      //   clientHeight: clientHeight,
      //   scrollHeight: scrollHeight,
      //   timestamp: new Date().toLocaleTimeString(),
      // });
      setContentHeight(height);
    }
  }, []);

  // 创建防抖版本的高度更新函数
  const debouncedUpdateContentHeight = useDebouncedCallback(updateContentHeight, 150);

  // 监听Content高度变化并更新状态
  useEffect(() => {
    // 初始更新（不需要防抖）
    updateContentHeight();

    // 监听窗口大小变化（使用防抖）
    const handleResize = () => {
      debouncedUpdateContentHeight();
    };

    window.addEventListener('resize', handleResize);

    // 使用ResizeObserver监听Content元素大小变化（使用防抖）
    let resizeObserver: ResizeObserver | null = null;
    if (contentRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        debouncedUpdateContentHeight();
      });
      resizeObserver.observe(contentRef.current);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [updateContentHeight, debouncedUpdateContentHeight]);

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === '/' || path === '/dashboard') return ['dashboard'];
    if (path === '/users') return ['users'];
    if (path === '/tasks') return ['tasks'];
    if (path === '/teams') return ['teams'];
    if (path === '/cache-test') return ['cache-test'];
    return ['dashboard'];
  };

  // 获取面包屑导航
  const getBreadcrumbItems = () => {
    const path = location.pathname;
    const items = [
      {
        href: '/dashboard',
        title: (
          <Space>
            <HomeOutlined />
            <span>首页</span>
          </Space>
        ),
      },
    ];

    if (path === '/users') {
      items.push({
        href: '/users',
        title: <span>用户管理</span>,
      });
    } else if (path === '/tasks') {
      items.push({
        href: '/tasks',
        title: <span>任务管理</span>,
      });
    } else if (path === '/teams') {
      items.push({
        href: '/teams',
        title: <span>团队协作</span>,
      });
    }

    return items;
  };

  // 添加标签页
  const addTab = (key: string, label: string) => {
    const existingTab = tabs.find(tab => tab.key === key);
    if (!existingTab) {
      setTabs([
        ...tabs,
        {
          key,
          label,
          closable: key !== 'dashboard',
        },
      ]);
    }
    setActiveTabKey(key);
  };

  // 移除标签页
  const removeTab = (targetKey: string) => {
    const newTabs = tabs.filter(tab => tab.key !== targetKey);
    setTabs(newTabs);

    // 清除对应的缓存
    clearTabCache(targetKey);

    if (activeTabKey === targetKey) {
      const lastTab = newTabs[newTabs.length - 1];
      if (lastTab) {
        setActiveTabKey(lastTab.key);
        navigate(`/${lastTab.key === 'dashboard' ? '' : lastTab.key}`);
      }
    }
  };

  // 标签页切换
  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    setAnimationKey(prev => prev + 1); // 触发动画
    navigate(`/${key === 'dashboard' ? '' : key}`);
  };

  // 菜单点击处理
  const handleMenuClick = ({ key }: { key: string }) => {
    const tabLabels = {
      dashboard: '仪表板',
      users: '用户管理',
      tasks: '任务管理',
      teams: '团队协作',
      'cache-test': '缓存测试',
    };

    const label = tabLabels[key as keyof typeof tabLabels];
    if (label) {
      addTab(key, label);
    }

    switch (key) {
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'users':
        navigate('/users');
        break;
      case 'tasks':
        navigate('/tasks');
        break;
      case 'teams':
        navigate('/teams');
        break;
      case 'cache-test':
        navigate('/cache-test');
        break;
      default:
        break;
    }
  };

  // 侧边栏菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: 'tasks',
      icon: <FileTextOutlined />,
      label: '任务管理',
    },
    {
      key: 'teams',
      icon: <TeamOutlined />,
      label: '团队协作',
    },
    {
      key: 'cache-test',
      icon: <DatabaseOutlined />,
      label: '缓存测试',
    },
    {
      key: 'sub1',
      label: '系统设置',
      icon: <SettingOutlined />,
      children: [
        {
          key: 'settings-basic',
          label: '基础设置',
        },
        {
          key: 'settings-permission',
          label: '权限管理',
        },
        {
          key: 'settings-logs',
          label: '系统日志',
        },
      ],
    },
  ];

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  return (
    <Layout className={styles.layout}>
      {/* 侧边栏 */}
      <Sider trigger={null} collapsible collapsed={collapsed} width={240} collapsedWidth={48} className={`${styles.sider} ${collapsed ? styles.siderCollapsed : ''}`}>
        {/* Logo 区域 */}
        <div className={`${styles.logo} ${collapsed ? styles.collapsed : styles.expanded}`}>
          <div className={`${styles.logoText} ${collapsed ? styles.collapsed : styles.expanded}`}>{collapsed ? 'S' : 'DB SQL MONITOR'}</div>
        </div>

        {/* 菜单 */}
        <Menu theme='light' mode='inline' selectedKeys={getSelectedKeys()} onClick={handleMenuClick} items={menuItems} className={styles.menu} />
      </Sider>

      {/* 主要内容区域 */}
      <Layout>
        {/* 顶部导航栏 */}
        <Header className={styles.header}>
          {/* 左侧：折叠按钮和面包屑 */}
          <Space size='middle'>
            <Button type='text' icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />} onClick={() => setCollapsed(!collapsed)} className={styles.triggerButton} />
            <Breadcrumb separator={<RightOutlined className={styles.breadcrumbSeparator} />} items={getBreadcrumbItems()} className={styles.breadcrumb} />
          </Space>

          {/* 右侧：用户信息和通知 */}
          <Space size='middle'>
            {/* 通知铃铛 */}
            <Badge count={5} size='small'>
              <Button type='text' icon={<BellOutlined />} className={styles.notificationButton} />
            </Badge>

            {/* 用户头像和下拉菜单 */}
            <Dropdown
              menu={{
                items: userMenuItems,
              }}
              placement='bottomRight'
              arrow
            >
              <Space className={styles.userDropdown}>
                <Avatar size={24} icon={<UserOutlined />} className={styles.userAvatar} />
                <span className={styles.userName}>管理员</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 标签页区域 */}
        <div className={styles.tabsContainer}>
          <div className={styles.tabsWrapper}>
            <Tabs
              type='editable-card'
              activeKey={activeTabKey}
              onChange={handleTabChange}
              onEdit={(targetKey, action) => {
                if (action === 'remove') {
                  removeTab(targetKey as string);
                }
              }}
              size='small'
              className={styles.customTabs}
              items={tabs.map(tab => ({
                key: tab.key,
                label: tab.label,
                closable: tab.closable,
              }))}
            />

            {/* 缓存控制区域 */}
            <div className={styles.cacheControls}>
              <Space size="small">
                <Tooltip title={cacheEnabled ? '关闭页面缓存' : '开启页面缓存'}>
                  <Switch
                    size="small"
                    checked={cacheEnabled}
                    onChange={setCacheEnabled}
                    checkedChildren={<DatabaseOutlined />}
                    unCheckedChildren={<DatabaseOutlined />}
                  />
                </Tooltip>
                <Tooltip title="清除所有缓存">
                  <Button
                    type="text"
                    size="small"
                    onClick={clearAllCache}
                    disabled={cachedComponents.size === 0}
                    className={styles.clearCacheBtn}
                  >
                    清除缓存 ({cachedComponents.size})
                  </Button>
                </Tooltip>
              </Space>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <Content ref={contentRef} className={styles.content}>
          <div className={styles.contentWrapper}>
            {/* 如果启用缓存，渲染所有标签页的内容 */}
            {cacheEnabled ? (
              <>
                {tabs.map(tab => (
                  <div
                    key={`cached-${tab.key}`}
                    style={{
                      display: tab.key === activeTabKey ? 'block' : 'none',
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <div
                      key={tab.key === activeTabKey ? `${tab.key}-${animationKey}` : tab.key}
                      className={tab.key === activeTabKey ? styles.fadeIn : ''}
                    >
                      {getCachedComponent(tab.key)}
                    </div>
                  </div>
                ))}
              </>
            ) : (
              /* 如果未启用缓存，使用传统的Outlet方式 */
              <div key={location.pathname} className={styles.fadeIn}>
                <Outlet
                  context={{
                    contentHeight,
                  }}
                />
              </div>
            )}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

// 保持向后兼容
export const ResponsiveLayout = DefaultLayout;
