import React, { useState } from 'react';
import { Form, Input, Button, Card, Space, message, DatePicker, Switch } from 'antd';
import { TaskGroupSelect } from '../common/TaskGroupSelect';
import dayjs from 'dayjs';

/**
 * 带有任务分组选择的任务表单示例
 * 展示在实际业务场景中如何使用 TaskGroupSelect 组件
 */
const TaskFormWithGroupSelect: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 表单提交处理
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('提交的表单数据:', values);
      message.success('任务创建成功！');

      // 重置表单
      form.resetFields();
    } catch (error) {
      message.error('任务创建失败: ' + error);
    } finally {
      setLoading(false);
    }
  };

  // 表单重置处理
  const handleReset = () => {
    form.resetFields();
    message.info('表单已重置');
  };

  return (
    <div className='p-6 max-w-2xl mx-auto'>
      <Card
        title='创建新任务'
        className='shadow-lg'
        extra={
          <Space>
            <Button onClick={handleReset}>重置</Button>
            <Button type='primary' loading={loading} onClick={() => form.submit()}>
              创建任务
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          initialValues={{
            status: true,
            startTime: dayjs('09:00', 'HH:mm'),
            endTime: dayjs('18:00', 'HH:mm'),
          }}
        >
          {/* 任务基本信息 */}
          <div className='mb-6'>
            <h3 className='text-lg font-semibold mb-4 text-gray-800'>基本信息</h3>

            <Form.Item
              name='taskName'
              label='任务名称'
              rules={[
                { required: true, message: '请输入任务名称' },
                { min: 2, max: 50, message: '任务名称长度应在2-50个字符之间' },
              ]}
            >
              <Input
                placeholder='请输入任务名称'
                className='rounded-md'
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item name='taskGroup' label='任务分组' rules={[{ required: true, message: '请选择任务分组' }]} tooltip='选择任务所属的分组，用于任务管理和分类'>
              <TaskGroupSelect placeholder='请选择任务分组' className='rounded-md' />
            </Form.Item>

            <Form.Item name='description' label='任务描述' rules={[{ max: 200, message: '描述不能超过200个字符' }]}>
              <Input.TextArea
                placeholder='请输入任务描述（可选）'
                rows={3}
                className='rounded-md'
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
                spellCheck={false}
              />
            </Form.Item>
          </div>

          {/* 执行配置 */}
          <div className='mb-6'>
            <h3 className='text-lg font-semibold mb-4 text-gray-800'>执行配置</h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Form.Item name='startTime' label='开始时间' rules={[{ required: true, message: '请选择开始时间' }]}>
                <DatePicker.TimePicker format='HH:mm' placeholder='选择开始时间' className='w-full rounded-md' />
              </Form.Item>

              <Form.Item name='endTime' label='结束时间' rules={[{ required: true, message: '请选择结束时间' }]}>
                <DatePicker.TimePicker format='HH:mm' placeholder='选择结束时间' className='w-full rounded-md' />
              </Form.Item>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Form.Item
                name='frequency'
                label='执行频率（分钟）'
                rules={[
                  { required: true, message: '请输入执行频率' },
                  { type: 'number', min: 1, max: 1440, message: '频率应在1-1440分钟之间' },
                ]}
              >
                <Input type='number' placeholder='请输入执行频率' suffix='分钟' className='rounded-md' />
              </Form.Item>

              <Form.Item name='retryCount' label='重试次数' rules={[{ type: 'number', min: 0, max: 10, message: '重试次数应在0-10次之间' }]}>
                <Input type='number' placeholder='请输入重试次数' suffix='次' className='rounded-md' />
              </Form.Item>
            </div>
          </div>

          {/* 任务状态 */}
          <div className='mb-6'>
            <h3 className='text-lg font-semibold mb-4 text-gray-800'>状态设置</h3>

            <Form.Item name='status' label='任务状态' valuePropName='checked' tooltip='启用后任务将按照设定的时间和频率自动执行'>
              <Switch checkedChildren='启用' unCheckedChildren='禁用' className='bg-gray-300' />
            </Form.Item>
          </div>

          {/* 提交按钮 */}
          <Form.Item className='mb-0'>
            <div className='flex justify-end space-x-3'>
              <Button size='large' onClick={handleReset} className='px-8'>
                重置表单
              </Button>
              <Button type='primary' htmlType='submit' loading={loading} size='large' className='px-8 bg-blue-600 hover:bg-blue-700'>
                {loading ? '创建中...' : '创建任务'}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>

      {/* 使用说明 */}
      <Card title='使用说明' className='mt-6'>
        <div className='text-sm text-gray-600 space-y-2'>
          <p>
            <strong>任务分组选择：</strong>
          </p>
          <ul className='list-disc list-inside ml-4 space-y-1'>
            <li>组件会自动从后端加载所有可用的任务分组</li>
            <li>支持搜索功能，可以快速找到目标分组</li>
            <li>支持清除选择，重新选择分组</li>
            <li>在表单验证中，任务分组为必选项</li>
          </ul>

          <p className='mt-4'>
            <strong>表单特性：</strong>
          </p>
          <ul className='list-disc list-inside ml-4 space-y-1'>
            <li>完整的表单验证，确保数据完整性</li>
            <li>响应式布局，适配不同屏幕尺寸</li>
            <li>友好的用户交互和反馈</li>
            <li>支持表单重置和提交状态显示</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default TaskFormWithGroupSelect;
