import React, { useState } from 'react';
import { Card, Space, Button, message, Form, Row, Col } from 'antd';
import { TaskGroupSelect } from './common/TaskGroupSelect';

/**
 * TaskGroupSelect 动态搜索功能测试页面
 */
const TaskGroupSelectTest: React.FC = () => {
  const [selectedGroup1, setSelectedGroup1] = useState<string>();
  const [selectedGroup2, setSelectedGroup2] = useState<string>();
  const [form] = Form.useForm();

  const handleGroupChange1 = (value: string) => {
    setSelectedGroup1(value);
    message.info(`初始化加载模式选择了分组: ${value}`);
  };

  const handleGroupChange2 = (value: string) => {
    setSelectedGroup2(value);
    message.info(`动态搜索模式选择了分组: ${value}`);
  };

  const handleFormSubmit = (values: any) => {
    console.log('表单数据:', values);
    message.success(`表单提交成功！选择的分组: ${values.taskGroup}`);
  };

  return (
    <div className='p-6 max-w-6xl mx-auto'>
      <h1 className='text-3xl font-bold mb-8 text-center text-gray-800'>TaskGroupSelect 动态搜索功能测试</h1>

      <Row gutter={[24, 24]}>
        {/* 初始化加载模式 */}
        <Col xs={24} lg={12}>
          <Card title='初始化加载模式' className='h-full' extra={<span className='text-blue-600 text-sm'>组件挂载时加载数据</span>}>
            <Space direction='vertical' className='w-full' size='large'>
              <div>
                <label className='block mb-2 font-medium text-gray-700'>选择任务分组：</label>
                <TaskGroupSelect value={selectedGroup1} onChange={handleGroupChange1} placeholder='请选择任务分组' className='w-full rounded-md' dynamicSearch={false} />
              </div>

              <div className='p-3 bg-gray-50 rounded-md'>
                <strong>当前选中的分组：</strong>
                <span className='ml-2 text-blue-600'>{selectedGroup1 || '未选择'}</span>
              </div>

              <div className='text-sm text-gray-600'>
                <p>
                  <strong>特点：</strong>
                </p>
                <ul className='list-disc list-inside ml-4 space-y-1'>
                  <li>组件挂载时自动加载数据</li>
                  <li>适合数据量不大的场景</li>
                  <li>用户体验更流畅</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 动态搜索模式 */}
        <Col xs={24} lg={12}>
          <Card title='动态搜索模式' className='h-full' extra={<span className='text-green-600 text-sm'>点击时才加载数据</span>}>
            <Space direction='vertical' className='w-full' size='large'>
              <div>
                <label className='block mb-2 font-medium text-gray-700'>选择任务分组：</label>
                <TaskGroupSelect value={selectedGroup2} onChange={handleGroupChange2} placeholder='请选择任务分组' className='w-full rounded-md' dynamicSearch={true} />
              </div>

              <div className='p-3 bg-gray-50 rounded-md'>
                <strong>当前选中的分组：</strong>
                <span className='ml-2 text-green-600'>{selectedGroup2 || '未选择'}</span>
              </div>

              <div className='text-sm text-gray-600'>
                <p>
                  <strong>特点：</strong>
                </p>
                <ul className='list-disc list-inside ml-4 space-y-1'>
                  <li>点击下拉框时才加载数据</li>
                  <li>减少初始化时的网络请求</li>
                  <li>适合数据量大或网络较慢的场景</li>
                  <li>首次点击时会显示加载状态</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 表单测试 */}
      <Card title='表单中使用动态搜索' className='mt-6'>
        <Form form={form} layout='vertical' onFinish={handleFormSubmit} className='max-w-md'>
          <Form.Item name='taskGroup' label='任务分组' rules={[{ required: true, message: '请选择任务分组' }]}>
            <TaskGroupSelect placeholder='请选择任务分组' dynamicSearch={true} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type='primary' htmlType='submit'>
                提交表单
              </Button>
              <Button onClick={() => form.resetFields()}>重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 使用说明 */}
      <Card title='使用说明' className='mt-6'>
        <div className='prose max-w-none'>
          <h3>如何启用动态搜索模式：</h3>
          <pre className='bg-gray-100 p-3 rounded text-sm overflow-x-auto'>
            {`<TaskGroupSelect 
  dynamicSearch={true}  // 启用动态搜索
  placeholder="请选择任务分组" 
/>`}
          </pre>

          <h3>测试步骤：</h3>
          <ol className='list-decimal list-inside space-y-2'>
            <li>观察左侧组件：页面加载时会自动请求数据</li>
            <li>观察右侧组件：页面加载时不会请求数据</li>
            <li>点击右侧下拉框：此时才会发起网络请求加载数据</li>
            <li>查看浏览器开发者工具的网络面板，观察请求时机</li>
          </ol>

          <h3>适用场景：</h3>
          <ul className='list-disc list-inside space-y-1'>
            <li>
              <strong>初始化加载模式：</strong>数据量小，用户经常使用的场景
            </li>
            <li>
              <strong>动态搜索模式：</strong>数据量大，网络较慢，或用户不一定会使用的场景
            </li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default TaskGroupSelectTest;
