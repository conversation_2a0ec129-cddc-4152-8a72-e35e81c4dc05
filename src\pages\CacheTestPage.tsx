import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Space, Typography, Divider, Alert } from 'antd';
import { PlusOutlined, MinusOutlined, ReloadOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * 缓存测试页面
 * 用于演示标签页缓存功能
 */
const CacheTestPage: React.FC = () => {
  const [counter, setCounter] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const [timestamp, setTimestamp] = useState('');

  // 组件挂载时记录时间戳
  useEffect(() => {
    const now = new Date().toLocaleString();
    setTimestamp(now);
    console.log('CacheTestPage 组件挂载时间:', now);
  }, []);

  const handleIncrement = () => {
    setCounter(prev => prev + 1);
  };

  const handleDecrement = () => {
    setCounter(prev => prev - 1);
  };

  const handleReset = () => {
    setCounter(0);
    setInputValue('');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>缓存测试页面</Title>
      
      <Alert
        message="缓存功能说明"
        description="这个页面用于测试标签页缓存功能。当开启缓存时，切换到其他标签页再回来，页面状态会被保持。关闭缓存时，每次切换都会重新渲染组件。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card title="组件状态测试" style={{ marginBottom: 24 }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Text strong>组件挂载时间: </Text>
            <Text code>{timestamp}</Text>
          </div>

          <Divider />

          <div>
            <Text strong>计数器: </Text>
            <Text style={{ fontSize: 24, color: '#1890ff' }}>{counter}</Text>
          </div>

          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={handleIncrement}
            >
              增加
            </Button>
            <Button 
              icon={<MinusOutlined />} 
              onClick={handleDecrement}
            >
              减少
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReset}
            >
              重置
            </Button>
          </Space>

          <Divider />

          <div>
            <Text strong>输入框测试: </Text>
            <Input
              placeholder="输入一些文字测试状态保持"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              style={{ marginTop: 8 }}
            />
            {inputValue && (
              <div style={{ marginTop: 8 }}>
                <Text>当前输入: </Text>
                <Text code>{inputValue}</Text>
              </div>
            )}
          </div>
        </Space>
      </Card>

      <Card title="测试步骤">
        <ol>
          <li>在计数器中点击几次"增加"按钮</li>
          <li>在输入框中输入一些文字</li>
          <li>切换到其他标签页（如用户管理）</li>
          <li>再切换回来查看状态是否保持</li>
          <li>尝试关闭缓存开关，重复上述步骤观察差异</li>
        </ol>
      </Card>
    </div>
  );
};

export default CacheTestPage;
