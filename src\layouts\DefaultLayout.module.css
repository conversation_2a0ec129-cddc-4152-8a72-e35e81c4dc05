/* CSS 变量定义 */
:root {
  --content-animation-duration: 0.5s;
  --content-animation-timing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --content-slide-distance: -60px;
  --color-bg-container: #fff;
}

/* 布局样式 */
.layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
.sider {
  background: var(--color-bg-container);
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  border-right: 1px solid #f0f0f0;
  transition: all 0.2s;
  width: 240px;
  min-width: 240px;
}

.siderCollapsed {
  width: 48px !important;
  min-width: 48px !important;
}

/* Logo 区域 */
.logo {
  height: 48px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.logo.collapsed {
  justify-content: center;
  padding: 0;
}

.logo.expanded {
  justify-content: flex-start;
  padding: 0 24px;
}

.logoText {
  font-weight: bold;
  color: #1890ff;
  transition: all 0.3s;
}

.logoText.collapsed {
  font-size: 20px;
}

.logoText.expanded {
  font-size: 18px;
}

/* 头部样式 */
.header {
  padding: 0 6px;
  background: var(--color-bg-container);
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 48px;
  line-height: 48px;
}

/* 折叠按钮 */
.triggerButton {
  font-size: 16px;
  width: 32px;
  height: 32px;
  transition: all 0.3s;
}

.triggerButton:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

/* 面包屑导航 */
.breadcrumb {
  font-size: 14px;
}

.breadcrumbSeparator {
  font-size: 10px;
}

/* 用户信息区域 */
.userArea {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notificationButton {
  font-size: 16px;
  width: 32px;
  height: 32px;
  transition: all 0.3s;
}

.notificationButton:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.userDropdown {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.userDropdown:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.userName {
  font-size: 14px;
  font-weight: 500;
}

.userAvatar {
  background-color: #1890ff;
}

/* 内容区域 */
.content {
  margin: 12px;
  /* 动态计算高度: 100vh - 48px(header) - 30px(tabs) - 24px(上下margin) - 12px(其它) */
  height: calc(100vh - 48px - 30px - 24px - 12px);
  min-height: calc(100vh - 48px - 30px - 24px - 12px);
  overflow: auto;
}

/* 内容包装器 - 页面切换动画 */
.contentWrapper {
  /* 从左往右滑入动画效果 */
  animation: fadeSlideIn var(--content-animation-duration) var(--content-animation-timing);
  transform: translateX(0);
  opacity: 1;
  will-change: transform, opacity;
}

/* Fade-Slide 组合动画 - 从左往右滑入 */
@keyframes fadeSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  30% {
    opacity: 0.3;
    transform: translateX(-30px);
  }
  70% {
    opacity: 0.8;
    transform: translateX(-8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade-Slide 快速版本 */
@keyframes fadeSlideInFast {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade-Slide 带模糊效果 */
@keyframes fadeSlideInBlur {
  0% {
    opacity: 0;
    transform: translateX(-25px);
    filter: blur(3px);
  }
  70% {
    opacity: 0.8;
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0);
  }
}

/* Fade-Slide 带缩放效果 */
@keyframes fadeSlideInScale {
  0% {
    opacity: 0;
    transform: translateX(-25px) scale(0.96);
  }
  60% {
    opacity: 0.7;
    transform: translateX(-5px) scale(0.99);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 菜单样式 */
.menu {
  border: none;
  font-size: 14px;
}

.menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.menu .ant-menu-submenu {
  margin: 4px 8px;
}

.menu .ant-menu-submenu > .ant-menu-submenu-title {
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sider {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
  }

  .header {
    padding: 0 16px;
  }

  .content {
    margin: 16px;
    padding: 16px;
  }
}

/* 动画效果 */
.fadeIn {
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--content-animation-duration) var(--content-animation-timing),
              transform var(--content-animation-duration) var(--content-animation-timing);
}

/* 缓存模式下的标签页内容动画 */
.cachedTabContent {
  opacity: 0;
  transform: translateX(-40px);
  transition: opacity var(--content-animation-duration) var(--content-animation-timing),
              transform var(--content-animation-duration) var(--content-animation-timing);
}

.cachedTabContent.active {
  opacity: 1;
  transform: translateX(0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 标签页样式 */
.tabsContainer {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  height: 30px;
  padding: 0 6px;
  display: flex;
  align-items: center;
}

.tabsWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.cacheControls {
  display: flex;
  align-items: center;
  margin-left: 16px;
  font-size: 12px;
  color: #666;
}

.clearCacheBtn {
  font-size: 12px !important;
  height: 22px !important;
  padding: 0 6px !important;
  color: #666 !important;
}

.clearCacheBtn:hover {
  color: #1890ff !important;
  background-color: #f0f8ff !important;
}

.clearCacheBtn:disabled {
  color: #ccc !important;
}

.tabContent {
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(var(--content-slide-distance));
  transition: opacity var(--content-animation-duration) var(--content-animation-timing),
              transform var(--content-animation-duration) var(--content-animation-timing);
  pointer-events: none;
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.tabContent.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
  visibility: visible;
  animation: fadeSlideIn var(--content-animation-duration) var(--content-animation-timing);
}

.tabContentContainer {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.customTabs {
  height: 30px !important;
  min-height: 30px !important;
}

.customTabs .ant-tabs-nav {
  margin: 0 !important;
  height: 30px !important;
}

.customTabs .ant-tabs-tab {
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 12px !important;
  margin: 0 2px !important;
  font-size: 12px !important;
}

.customTabs .ant-tabs-tab-btn {
  height: 28px !important;
  line-height: 28px !important;
}

.customTabs .ant-tabs-tab-remove {
  margin-left: 4px !important;
  font-size: 10px !important;
}

.customTabs .ant-tabs-nav-wrap {
  height: 30px !important;
}

.customTabs .ant-tabs-nav-list {
  height: 30px !important;
}

/* 滚动条样式 */
.sider::-webkit-scrollbar {
  width: 6px;
}

.sider::-webkit-scrollbar-track {
  background: transparent;
}

.sider::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.sider::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
